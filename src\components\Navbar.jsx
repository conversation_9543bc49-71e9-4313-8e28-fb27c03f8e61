import React from 'react'

const Navbar = () => {
  return (
    <div className='flex flex-row justify-between items-center bg-gradient-to-r from-[#CFE3EC] to-[#84C4DA] backdrop-blur-md rounded-2xl p-6 fixed top-4 left-4 right-4 z-50 shadow-2xl border border-emerald-600/30'>
      <h1 className='text-black font-bold text-3xl font-serif tracking-wide drop-shadow-lg'>
         Junglee
      </h1>
      <div className='flex flex-row gap-8 text-lg font-semibold items-center'>
        <a href="#" className='text-emerald-100 hover:text-emerald-300 transition-all duration-300 hover:scale-110 px-4 py-2 rounded-lg hover:bg-emerald-600/30 backdrop-blur-sm'>
          Home
        </a>
        <a href="#" className='text-emerald-100 hover:text-emerald-300 transition-all duration-300 hover:scale-110 px-4 py-2 rounded-lg hover:bg-emerald-600/30 backdrop-blur-sm'>
          About
        </a>
        <a href="#" className='text-emerald-100 hover:text-emerald-300 transition-all duration-300 hover:scale-110 px-4 py-2 rounded-lg hover:bg-emerald-600/30 backdrop-blur-sm'>
          Explore
        </a>
        <button className='bg-gradient-to-r from-emerald-500 to-green-600 text-white px-6 py-2 rounded-full font-semibold hover:from-emerald-600 hover:to-green-700 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-emerald-500/25'>
          Adventure
        </button>
      </div>
    </div>
  )
}

export default Navbar
