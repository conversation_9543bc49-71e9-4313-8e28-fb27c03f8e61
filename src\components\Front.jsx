import React from 'react'
import jungleImg3 from '../images/final123.png';
import hathiImg from '../images/elephant.png';
import Navbar from './Navbar';

const Front = () => {
  return (
    <div className=" overflow-hidden">
        <img src={jungleImg3} alt="Jungle 3" className="w-full h-auto object-contain absolute inset-0 z-0 blur-sm" />
      <div className="w-[66%] h-[85%] z-10 flex items-center justify-center bg-transparent  rounded-2xl shadow-2xl border-2 border-white absolute top-1/2 left-1/2 mt-10" style={{transform: 'translate(-50%, -50%)'}}>
     
      <h1 className="text-9xl font-bold text-white absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 "><span></span></h1>
        <img src={hathiImg} alt="Hathi" className="max-w-full max-h-full object-contain rounded-2xl" />
      </div>
      
    </div>
  )
}

export default Front
