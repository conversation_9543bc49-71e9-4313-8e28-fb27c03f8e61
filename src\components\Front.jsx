import React from 'react'
import patherImg from '../images/panther1.png';
import Navbar from './Navbar';

const Front = () => {
  return (
    
    <div className='bg-[#EAF3F5] w-screen h-screen overflow-hidden relative flex justify-center items-center '>

      <div className='bg-gradient-to-r from-[#EAF3F5] to-[#CFE3EC] w-[90%] h-[90%] overflow-hidden relative rounded-3xl shadow-2xl shadow-black/100 mt-12'>
      
      <div className='h-[190vh] w-[190vh] rounded-full bg-[#CFE3EC] absolute top-0 right-0 translate-x-1/2 -translate-y-1/2'></div>
      <div className="absolute top-0 right-0 h-1/2 w-[60px] bg-gradient-to-t from-[#3DABA4] to-[#95DDD1] z-50"></div>
      <div className="absolute bottom-0 right-0 h-1/2 w-[60px]  bg-gradient-to-b from-[#85C5DB] to-[#578392] z-50"></div>
      <div className='absolute -bottom-60 left-40 h-[350px] w-[350px] rounded-full bg-gradient-to-l from-[#CFE3EC] to-[#DDE6E8] z-50 ml-20'></div>
      <Navbar />
      {/* <div className='absolute bottom-20 right-40 h-[90px] w-[400px]  bg-[#86C8DD] opacity-40   z-50 mr-20 rounded-2xl border-2 border-[#6DA3B5] '></div> */}
      {/* Pather image div */}
      <div className='absolute left-1/2 bottom-5 transform -translate-x-1/2 z-50'>
        <img src={patherImg} alt="Pather" className="h-[700px] w-full object-contain " />
      </div>
    </div> 
    </div>
  )
}

export default Front
