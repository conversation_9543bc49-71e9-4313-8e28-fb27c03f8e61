import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import patherImg from '../images/panther1.png';
import Navbar from './Navbar';

const Front = () => {
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    setIsLoaded(true)
  }, [])

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 1,
        staggerChildren: 0.3
      }
    }
  }

  const slideInLeft = {
    hidden: { x: -100, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: { duration: 0.8, ease: "easeOut" }
    }
  }

  const slideInRight = {
    hidden: { x: 100, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: { duration: 0.8, ease: "easeOut" }
    }
  }

  const fadeInUp = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.8, ease: "easeOut" }
    }
  }

  const scaleIn = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: { duration: 1, ease: "easeOut" }
    }
  }

  return (
    <motion.div
      className='bg-[#EAF3F5] w-screen h-screen overflow-hidden relative flex justify-center items-center'
      variants={containerVariants}
      initial="hidden"
      animate={isLoaded ? "visible" : "hidden"}
    >
      <Navbar />

      <motion.div
        className='bg-gradient-to-r from-[#EAF3F5] to-[#CFE3EC] w-[90%] h-[90%] overflow-hidden relative rounded-3xl shadow-2xl shadow-[#000000]/100'
        variants={scaleIn}
      >

        {/* Animated Background Elements */}
        <motion.div
          className='h-[190vh] w-[190vh] rounded-full bg-[#CFE3EC] absolute top-0 right-0 translate-x-1/2 -translate-y-1/2'
          animate={{
            rotate: [0, 360],
            scale: [1, 1.05, 1]
          }}
          transition={{
            rotate: { duration: 20, repeat: Infinity, ease: "linear" },
            scale: { duration: 4, repeat: Infinity, ease: "easeInOut" }
          }}
        />

        <motion.div
          className="absolute top-0 right-0 h-1/2 w-[60px] bg-gradient-to-t from-[#3DABA4] to-[#95DDD1] z-50"
          animate={{ y: [0, -10, 0] }}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
        />

        <motion.div
          className="absolute bottom-0 right-0 h-1/2 w-[60px] bg-gradient-to-b from-[#85C5DB] to-[#578392] z-50"
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut", delay: 1.5 }}
        />

        <motion.div
          className='absolute -bottom-60 left-40 h-[350px] w-[350px] rounded-full bg-gradient-to-l from-[#CFE3EC] to-[#DDE6E8] z-50 ml-20'
          animate={{
            x: [0, 20, 0],
            y: [0, -15, 0]
          }}
          transition={{ duration: 5, repeat: Infinity, ease: "easeInOut" }}
        />

        {/* Main Text Content */}
        <div className='absolute top-20 left-10 z-50 max-w-md'>
          <motion.h1
            className='text-6xl font-bold text-[#2D5A5A] mb-4 leading-tight'
            variants={slideInLeft}
          >
            Welcome to the
            <span className='block text-[#3DABA4] text-7xl'>Wild</span>
          </motion.h1>

          <motion.p
            className='text-xl text-[#578392] mb-6 leading-relaxed'
            variants={fadeInUp}
          >
            Discover the untamed beauty of nature and embark on an extraordinary adventure through the jungle.
          </motion.p>

          <motion.button
            className='bg-gradient-to-r from-[#3DABA4] to-[#578392] text-white px-8 py-4 rounded-full text-lg font-semibold hover:from-[#2D8B85] hover:to-[#456B73] transition-all duration-300 shadow-lg'
            variants={fadeInUp}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            🌿 Explore Now
          </motion.button>
        </div>

        {/* Side Text Content */}
        <motion.div
          className='absolute top-1/2 right-20 transform -translate-y-1/2 z-50 text-right max-w-xs'
          variants={slideInRight}
        >
          <h2 className='text-3xl font-bold text-[#2D5A5A] mb-3'>
            Majestic Wildlife
          </h2>
          <p className='text-lg text-[#578392] mb-4'>
            Meet the incredible creatures that call the jungle home.
          </p>
          <div className='flex justify-end space-x-2'>
            <span className='text-2xl'>🐆</span>
            <span className='text-2xl'>🦎</span>
            <span className='text-2xl'>🐍</span>
          </div>
        </motion.div>

        {/* Bottom Stats */}
        <motion.div
          className='absolute bottom-10 left-10 z-50 flex space-x-8'
          variants={fadeInUp}
        >
          <div className='text-center'>
            <div className='text-3xl font-bold text-[#3DABA4]'>500+</div>
            <div className='text-sm text-[#578392]'>Species</div>
          </div>
          <div className='text-center'>
            <div className='text-3xl font-bold text-[#3DABA4]'>1000+</div>
            <div className='text-sm text-[#578392]'>Adventures</div>
          </div>
          <div className='text-center'>
            <div className='text-3xl font-bold text-[#3DABA4]'>24/7</div>
            <div className='text-sm text-[#578392]'>Exploration</div>
          </div>
        </motion.div>

        {/* Animated Panther */}
        <motion.div
          className='absolute left-1/2 bottom-5 transform -translate-x-1/2 z-50'
          variants={scaleIn}
          whileHover={{ scale: 1.05 }}
          animate={{
            y: [0, -10, 0]
          }}
          transition={{
            y: { duration: 4, repeat: Infinity, ease: "easeInOut" }
          }}
        >
          <img src={patherImg} alt="Panther" className="h-[700px] w-full object-contain" />
        </motion.div>

        {/* Floating Elements */}
        <motion.div
          className="absolute top-32 left-1/3 w-4 h-4 bg-[#95DDD1] rounded-full opacity-60"
          animate={{
            y: [0, -20, 0],
            opacity: [0.6, 1, 0.6]
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        <motion.div
          className="absolute bottom-40 right-1/3 w-6 h-6 bg-[#85C5DB] rounded-full opacity-50"
          animate={{
            y: [0, -30, 0],
            x: [0, 10, 0],
            opacity: [0.5, 0.8, 0.5]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
        />
      </motion.div>
    </motion.div>
  )
}

export default Front
