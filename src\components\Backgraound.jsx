import React from 'react'

const Backgraound = () => {
  return (
    <div className="w-full h-screen bg-gradient-to-br from-purple-800 via-purple-700 to-purple-900 relative overflow-hidden">
      {/* Paper texture overlay */}
      <div
        className="absolute inset-0 opacity-30"
        style={{
          backgroundImage: `
            radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 1px, transparent 1px),
            radial-gradient(circle at 80% 50%, rgba(255,255,255,0.1) 1px, transparent 1px),
            radial-gradient(circle at 40% 20%, rgba(255,255,255,0.05) 1px, transparent 1px),
            radial-gradient(circle at 60% 80%, rgba(255,255,255,0.05) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.02) 1px, transparent 1px),
            linear-gradient(0deg, rgba(255,255,255,0.02) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px, 30px 30px, 80px 80px, 60px 60px, 20px 20px, 20px 20px'
        }}
      />

      {/* Additional paper grain texture */}
      <div
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage: `
            repeating-linear-gradient(
              45deg,
              rgba(255,255,255,0.03) 0px,
              rgba(255,255,255,0.03) 1px,
              transparent 1px,
              transparent 3px
            ),
            repeating-linear-gradient(
              -45deg,
              rgba(255,255,255,0.02) 0px,
              rgba(255,255,255,0.02) 1px,
              transparent 1px,
              transparent 4px
            )
          `
        }}
      />

      {/* Subtle noise pattern for paper effect */}
      <div
        className="absolute inset-0 opacity-40"
        style={{
          backgroundImage: `
            radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0.5px, transparent 0.5px),
            radial-gradient(circle at 75% 75%, rgba(255,255,255,0.08) 0.5px, transparent 0.5px),
            radial-gradient(circle at 50% 10%, rgba(255,255,255,0.06) 0.5px, transparent 0.5px),
            radial-gradient(circle at 10% 90%, rgba(255,255,255,0.06) 0.5px, transparent 0.5px)
          `,
          backgroundSize: '15px 15px, 25px 25px, 35px 35px, 45px 45px'
        }}
      />
    </div>
  )
}

export default Backgraound
